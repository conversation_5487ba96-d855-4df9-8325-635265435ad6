{"name": "mcp-<PERSON><PERSON><PERSON>", "type": "module", "version": "0.0.10", "packageManager": "pnpm@9.14.4+sha512.c8180b3fbe4e4bca02c94234717896b5529740a6cbadf19fa78254270403ea2f27d4e1d46a08a0f56c89b63dc8ebfd3ee53326da720273794e6200fcf0d184ab", "description": "MCP server for fetch deepwiki.com and turn content into LLM readable markdown", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "license": "MIT", "homepage": "https://github.com/regenrek/deepwiki-mcp", "repository": {"type": "git", "url": "git+https://github.com/regenrek/deepwiki-mcp"}, "keywords": ["mcp", "mcp-starter", "model-context-protocol"], "exports": {".": "./dist/index.mjs", "./cli": "./bin/cli.mjs"}, "bin": {"mcp-instruct": "./bin/cli.mjs"}, "files": ["bin", "dist"], "engines": {"node": ">=18.0.0"}, "scripts": {"build": "unbuild && npm run chmod-run", "chmod-run": "node -e \"fs.chmodSync('dist/index.mjs', '755'); if (require('fs').existsSync('dist/cli.mjs')) require('fs').chmodSync('dist/cli.mjs', '755');\"", "start": "nodemon --exec 'tsx src/index.ts'", "dev:prepare": "nr build", "inspect": "npx @modelcontextprotocol/inspector@latest", "dev": "npx concurrently 'unbuild --stub' 'npm run inspect'", "run-cli": "node bin/cli.mjs", "dev-stdio": "npx concurrently 'npm run run-cli' 'npm run inspect node ./bin/cli.mjs'", "dev-http": "npx concurrently 'npm run run-cli -- --http --port 4200' 'npm run inspect http://localhost:4200/mcp'", "dev-sse": "npx concurrently 'npm run run-cli -- --sse --port 4201' 'npm run inspect http://localhost:4201/sse'", "lint": "eslint", "lint:fix": "eslint --fix", "typecheck": "tsc --noEmit", "test": "vitest", "release": "tsx scripts/release.ts"}, "dependencies": {"@chatmcp/sdk": "^1.0.6", "@modelcontextprotocol/sdk": "^1.9.0", "@vitest/eslint-plugin": "^1.1.43", "citty": "^0.1.6", "eslint": "^9.25.1", "h3": "^1.15.1", "hast-util-from-html": "^2.0.3", "hast-util-sanitize": "^5.0.2", "linkedom": "^0.18.9", "mcp-deepwiki": "^0.0.9", "ofetch": "^1.4.1", "p-queue": "^8.1.0", "rehype-parse": "^9.0.1", "rehype-remark": "^10.0.1", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1", "remark-stringify": "^11.0.0", "robots-parser": "^3.0.1", "undici": "^7.8.0", "unified": "^11.0.5", "unist-util-visit": "^5.0.0", "wink-eng-lite-web-model": "^1.0.0", "wink-nlp": "^1.10.0", "zod": "^3.24.3"}, "devDependencies": {"@antfu/eslint-config": "^4.12.0", "@types/node": "^22.14.1", "dotenv": "^16.5.0", "esbuild": "^0.25.2", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "tsx": "^4.19.3", "typescript": "^5.8.3", "unbuild": "^3.5.0", "vite": "^6.3.1", "vitest": "^3.2.3"}}